import 'package:flutter/material.dart';
import 'package:get/get.dart';

class SmoothAuthOverlay {
  static OverlayEntry? _overlayEntry;
  
  static void show() {
    if (_overlayEntry != null) return;
    
    _overlayEntry = OverlayEntry(
      builder: (context) => Container(
        color: Colors.transparent,
        child: const Center(
          child: CircularProgressIndicator(
            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
          ),
        ),
      ),
    );
    
    Overlay.of(Get.context!).insert(_overlayEntry!);
  }
  
  static void hide() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }
  
  static Future<T?> showDuring<T>(Future<T?> Function() action) async {
    try {
      show();
      return await action();
    } finally {
      hide();
    }
  }
}
