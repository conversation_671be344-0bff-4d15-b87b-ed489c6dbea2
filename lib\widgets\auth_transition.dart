import 'package:flutter/material.dart';
import 'package:get/get.dart';

/// Custom transition for authentication flows to prevent black overlay
class AuthTransition extends CustomTransition {
  @override
  Widget buildTransition(
    BuildContext context,
    Curve? curve,
    Alignment? alignment,
    Animation<double> animation,
    Animation<double> secondaryAnimation,
    Widget child,
  ) {
    return FadeTransition(
      opacity: CurvedAnimation(
        parent: animation,
        curve: curve ?? Curves.easeInOut,
      ),
      child: child,
    );
  }
}

/// Extension methods for smooth auth navigation
extension SmoothAuthNavigation on GetInterface {
  /// Navigate to a page with auth-specific transition
  Future<T?>? toAuth<T>(Widget page) {
    return Get.to<T>(
      () => page,
      transition: Transition.custom,
      customTransition: AuthTransition(),
      duration: const Duration(milliseconds: 200),
      opaque: false,
      popGesture: false,
    );
  }
  
  /// Replace all pages with auth-specific transition
  Future<T?>? offAllAuth<T>(Widget page) {
    return Get.offAll<T>(
      () => page,
      transition: Transition.custom,
      customTransition: AuthTransition(),
      duration: const Duration(milliseconds: 200),
      opaque: false,
      popGesture: false,
    );
  }
}
